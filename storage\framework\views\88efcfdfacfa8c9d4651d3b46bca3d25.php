<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
        <title>Responsive Portfolio Website</title>
        <!-- Fonts -->
       <link rel="stylesheet" href="<?php echo e(asset('assets/fonts/unicons/css/line.css')); ?>">
        <!-- CSS -->
         <!-- SWIPER CSS -->
        <link rel="stylesheet" href="<?php echo e(asset('assets/css/swiper-bundle.min.css')); ?>">
        <link rel="stylesheet" href="<?php echo e(asset('assets/css/styles.css')); ?>">
        <?php if(file_exists(public_path('build/manifest.json')) || file_exists(public_path('hot'))): ?>
            <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
        <?php endif; ?>
</head>
<body>
        <!--==================== MAIN ====================-->
        <?php echo $__env->make('layouts.pages.header', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

        <!--==================== MAIN ====================-->
        <main class="main">
            <?php echo $__env->yieldContent('content'); ?>
        </main>

        <!--==================== FOOTER ====================-->
        <?php echo $__env->make('layouts.pages.footer', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        
        <!--==================== SCROLL TOP ====================-->
        <a href="#" class="scrollup" id="scroll-up">
          <i class="uil uil-arrow-up scrollup_icon"></i>
        </a>

        <!--==================== SWIPER JS ====================-->
        <script src="<?php echo e(asset('assets/js/swiper-bundle.min.js')); ?>"></script>

        <!--==================== MAIN JS ====================-->
        <script src="<?php echo e(asset('assets/js/main.js')); ?>"></script>

        <!-- SweetAlert2 -->
        <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

        <!-- Flash Messages -->
        <?php echo $__env->make('includes.flash_message', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    </body>
</html>
<?php /**PATH C:\inetpub\wwwroot\Laravel-Portfolio\resources\views/layouts/pages/base.blade.php ENDPATH**/ ?>